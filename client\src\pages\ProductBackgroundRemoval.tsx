import { useState, useRef } from "react";
import { useTranslation, type Language } from "@/lib/i18n";
import { useToast } from "@/hooks/use-toast";
import { type ProcessingStatus } from "@/lib/types";

// Components
import { ScenarioHero } from "@/components/ScenarioHero";
import { ImageUploader } from "@/components/ImageUploader";
import { ProcessingModal } from "@/components/ProcessingModal";
import { ImageComparison } from "@/components/ImageComparison";
import { BeforeAfterGallery } from "@/components/BeforeAfterGallery";
import { StepByStepGuide } from "@/components/StepByStepGuide";
import { ScenarioFeatures } from "@/components/ScenarioFeatures";
import { SEOHead } from "@/components/SEOHead";

interface ProductBackgroundRemovalProps {
  language: Language;
}

export default function ProductBackgroundRemoval({ language }: ProductBackgroundRemovalProps) {
  const { t } = useTranslation(language);
  const { toast } = useToast();
  const uploadSectionRef = useRef<HTMLDivElement>(null);

  // State management
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [processId, setProcessId] = useState<number | null>(null);
  const [showProcessing, setShowProcessing] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus | null>(null);

  // Scroll to upload section
  const scrollToUpload = () => {
    uploadSectionRef.current?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'center'
    });
  };

  // Handle upload start
  const handleUploadStart = (file: File, id: number) => {
    setUploadedFile(file);
    setProcessId(id);
    setShowProcessing(true);
    setShowResults(false);
  };

  // Handle upload complete
  const handleUploadComplete = (file: File, id: number) => {
    setUploadedFile(file);
    setProcessId(id);
    setShowProcessing(true);
  };

  // Handle processing complete
  const handleProcessingComplete = (status: ProcessingStatus) => {
    setProcessingStatus(status);
    setShowProcessing(false);
    setShowResults(true);
  };

  // Handle process another image
  const handleProcessAnother = () => {
    setUploadedFile(null);
    setProcessId(null);
    setShowResults(false);
    setProcessingStatus(null);
    scrollToUpload();
  };

  // Handle download
  const handleDownload = () => {
    if (processingStatus?.processedPath) {
      const link = document.createElement('a');
      link.href = `/uploads/${processingStatus.processedPath}`;
      link.download = `product-no-bg-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: t("results.download.success"),
        description: t("results.download.success_desc"),
      });
    }
  };

  return (
    <>
      {/* SEO Head */}
      <SEOHead
        title={t("products.hero.title") + " - " + t("products.hero.subtitle")}
        description={t("products.hero.description")}
        keywords="product background removal, ecommerce photo editing, product photography, transparent background, PNG download, online photo editor, product images, e-commerce tools, 产品抠图, 电商图片处理"
        language={language}
        canonical={`/remove-background-from-products`}
      />

      {/* Hero Section */}
      <ScenarioHero
        language={language}
        scenario="products"
        onScrollToUpload={scrollToUpload}
      />

      {/* Upload Section */}
      <section ref={uploadSectionRef} className="py-16 lg:py-24" id="upload">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {t("upload.title")}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {t("products.hero.description")}
            </p>
          </div>

          <ImageUploader
            language={language}
            onUploadStart={handleUploadStart}
            onUploadComplete={handleUploadComplete}
          />
        </div>
      </section>

      {/* Processing Modal */}
      <ProcessingModal
        open={showProcessing}
        language={language}
        onComplete={handleProcessingComplete}
      />

      {/* Results Section */}
      {showResults && uploadedFile && processingStatus && (
        <ImageComparison
          language={language}
          originalImage={URL.createObjectURL(uploadedFile)}
          processedImage={`/uploads/${processingStatus.processedPath}`}
          onProcessAnother={handleProcessAnother}
          onDownload={handleDownload}
        />
      )}

      {/* Before/After Gallery */}
      <BeforeAfterGallery
        language={language}
        scenario="products"
        onTryNow={scrollToUpload}
      />

      {/* Step-by-Step Guide */}
      <StepByStepGuide
        language={language}
        scenario="products"
        onStartNow={scrollToUpload}
      />

      {/* Scenario Features */}
      <ScenarioFeatures
        language={language}
        scenario="products"
      />

      {/* FAQ Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {t("faq.title")}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t("faq.description")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("products.faq.q1")}
              </h3>
              <p className="text-gray-600">
                {t("products.faq.a1")}
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("products.faq.q2")}
              </h3>
              <p className="text-gray-600">
                {t("products.faq.a2")}
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("products.faq.q3")}
              </h3>
              <p className="text-gray-600">
                {t("products.faq.a3")}
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("products.faq.q4")}
              </h3>
              <p className="text-gray-600">
                {t("products.faq.a4")}
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { Upload, Zap, Download, ArrowRight, CheckCircle } from "lucide-react";

interface StepByStepGuideProps {
  language: Language;
  scenario: 'products' | 'animals' | 'cars';
  onStartNow: () => void;
}

export function StepByStepGuide({ language, scenario, onStartNow }: StepByStepGuideProps) {
  const { t } = useTranslation(language);

  const steps = [
    {
      number: 1,
      icon: Upload,
      title: t(`${scenario}.steps.step1.title`),
      description: t(`${scenario}.steps.step1.description`),
      tip: t(`${scenario}.steps.step1.tip`),
      color: "bg-blue-500"
    },
    {
      number: 2,
      icon: Zap,
      title: t(`${scenario}.steps.step2.title`),
      description: t(`${scenario}.steps.step2.description`),
      tip: t(`${scenario}.steps.step2.tip`),
      color: "bg-purple-500"
    },
    {
      number: 3,
      icon: Download,
      title: t(`${scenario}.steps.step3.title`),
      description: t(`${scenario}.steps.step3.description`),
      tip: t(`${scenario}.steps.step3.tip`),
      color: "bg-green-500"
    }
  ];

  return (
    <section className="py-16 lg:py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {t(`${scenario}.steps.title`)}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t(`${scenario}.steps.description`)}
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {steps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <Card key={step.number} className="relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group">
                <CardContent className="p-8 text-center">
                  {/* Step Number */}
                  <div className="absolute top-4 right-4">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-sm font-bold text-gray-600">
                      {step.number}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className={`w-16 h-16 rounded-full ${step.color} flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-semibold mb-4">{step.title}</h3>

                  {/* Description */}
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {step.description}
                  </p>

                  {/* Tip */}
                  <div className="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
                    <div className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                      <p className="text-sm text-gray-700 font-medium">
                        <span className="font-semibold">{t('common.tip')}:</span> {step.tip}
                      </p>
                    </div>
                  </div>
                </CardContent>

                {/* Connection Line (except for last step) */}
                {index < steps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gray-300 transform -translate-y-1/2 z-10">
                    <ArrowRight className="w-4 h-4 text-gray-400 absolute -top-2 right-0" />
                  </div>
                )}
              </Card>
            );
          })}
        </div>

        {/* Additional Tips Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 mb-12">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">{t(`${scenario}.steps.tips_title`)}</h3>
            <div className="grid md:grid-cols-2 gap-6 text-left">
              <div className="space-y-3">
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700">{t(`${scenario}.steps.tip1`)}</p>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700">{t(`${scenario}.steps.tip2`)}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700">{t(`${scenario}.steps.tip3`)}</p>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                  <p className="text-gray-700">{t(`${scenario}.steps.tip4`)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-6">
            {t(`${scenario}.steps.cta_text`)}
          </p>
          <Button 
            onClick={onStartNow}
            size="lg" 
            className="gradient-bg hover:opacity-90 text-white px-8 py-3"
          >
            {t(`${scenario}.steps.cta_button`)}
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}

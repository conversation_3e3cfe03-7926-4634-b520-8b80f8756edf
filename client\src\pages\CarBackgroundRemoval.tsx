import { useState, useRef } from "react";
import { useTranslation, type Language } from "@/lib/i18n";
import { useToast } from "@/hooks/use-toast";
import { type ProcessingStatus } from "@/lib/types";

// Components
import { ScenarioHero } from "@/components/ScenarioHero";
import { ImageUploader } from "@/components/ImageUploader";
import { ProcessingModal } from "@/components/ProcessingModal";
import { ImageComparison } from "@/components/ImageComparison";
import { BeforeAfterGallery } from "@/components/BeforeAfterGallery";
import { StepByStepGuide } from "@/components/StepByStepGuide";
import { ScenarioFeatures } from "@/components/ScenarioFeatures";
import { SEOHead } from "@/components/SEOHead";

interface CarBackgroundRemovalProps {
  language: Language;
}

export default function CarBackgroundRemoval({ language }: CarBackgroundRemovalProps) {
  const { t } = useTranslation(language);
  const { toast } = useToast();
  const uploadSectionRef = useRef<HTMLDivElement>(null);

  // State management
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [processId, setProcessId] = useState<number | null>(null);
  const [showProcessing, setShowProcessing] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus | null>(null);

  // Scroll to upload section
  const scrollToUpload = () => {
    uploadSectionRef.current?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'center'
    });
  };

  // Handle upload start
  const handleUploadStart = (file: File, id: number) => {
    setUploadedFile(file);
    setProcessId(id);
    setShowProcessing(true);
    setShowResults(false);
  };

  // Handle upload complete
  const handleUploadComplete = (file: File, id: number) => {
    setUploadedFile(file);
    setProcessId(id);
    setShowProcessing(true);
  };

  // Handle processing complete
  const handleProcessingComplete = (status: ProcessingStatus) => {
    setProcessingStatus(status);
    setShowProcessing(false);
    setShowResults(true);
  };

  // Handle process another image
  const handleProcessAnother = () => {
    setUploadedFile(null);
    setProcessId(null);
    setShowResults(false);
    setProcessingStatus(null);
    scrollToUpload();
  };

  // Handle download
  const handleDownload = () => {
    if (processingStatus?.processedPath) {
      const link = document.createElement('a');
      link.href = `/uploads/${processingStatus.processedPath}`;
      link.download = `car-no-bg-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: t("results.download.success"),
        description: t("results.download.success_desc"),
      });
    }
  };

  return (
    <>
      {/* SEO Head */}
      <SEOHead
        title={t("cars.hero.title") + " - " + t("cars.hero.subtitle")}
        description={t("cars.hero.description")}
        keywords="car background removal, vehicle photo editing, automotive photography, dealership photos, car images, auto photography, vehicle marketing, 汽车抠图, 车辆照片去背景"
        language={language}
        canonical={`/remove-background-from-cars`}
      />

      {/* Hero Section */}
      <ScenarioHero
        language={language}
        scenario="cars"
        onScrollToUpload={scrollToUpload}
      />

      {/* Upload Section */}
      <section ref={uploadSectionRef} className="py-16 lg:py-24" id="upload">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {t("upload.title")}
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              {t("cars.hero.description")}
            </p>
          </div>

          <ImageUploader
            language={language}
            onUploadStart={handleUploadStart}
            onUploadComplete={handleUploadComplete}
          />
        </div>
      </section>

      {/* Processing Modal */}
      <ProcessingModal
        open={showProcessing}
        language={language}
        onComplete={handleProcessingComplete}
      />

      {/* Results Section */}
      {showResults && uploadedFile && processingStatus && (
        <ImageComparison
          language={language}
          originalImage={URL.createObjectURL(uploadedFile)}
          processedImage={`/uploads/${processingStatus.processedPath}`}
          onProcessAnother={handleProcessAnother}
          onDownload={handleDownload}
        />
      )}

      {/* Before/After Gallery */}
      <BeforeAfterGallery
        language={language}
        scenario="cars"
        onTryNow={scrollToUpload}
      />

      {/* Step-by-Step Guide */}
      <StepByStepGuide
        language={language}
        scenario="cars"
        onStartNow={scrollToUpload}
      />

      {/* Scenario Features */}
      <ScenarioFeatures
        language={language}
        scenario="cars"
      />

      {/* FAQ Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              {t("faq.title")}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t("faq.description")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("cars.faq.q1")}
              </h3>
              <p className="text-gray-600">
                {t("cars.faq.a1")}
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("cars.faq.q2")}
              </h3>
              <p className="text-gray-600">
                {t("cars.faq.a2")}
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("cars.faq.q3")}
              </h3>
              <p className="text-gray-600">
                {t("cars.faq.a3")}
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-3">
                {t("cars.faq.q4")}
              </h3>
              <p className="text-gray-600">
                {t("cars.faq.a4")}
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

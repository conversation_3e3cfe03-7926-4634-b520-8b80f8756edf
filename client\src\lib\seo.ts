import { type Language } from './i18n';

export interface SEOConfig {
  title: string;
  description: string;
  keywords: string;
  canonical?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
  jsonLd?: object;
}

export const defaultSEOConfig: Record<Language, SEOConfig> = {
  en: {
    title: "Free AI Background Remover - Remove Background Online | Remove bg - Fast Transparent PNG",
    description: "Free AI background remover tool. Remove background from product photos, car images, people portraits in 5 seconds. Generate transparent PNG downloads instantly. 100% free photo editor background removal with AI precision.",
    keywords: "remove background, background remover, free background remover, AI background remover, transparent PNG, cut out image, photo editor background, remove background from product photos, background remover for car images, remove background from logo, fast background removal, instant PNG, 5 second processing",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  zh: {
    title: "免费在线图片去背景工具 - AI智能抠图 | Remove bg - 快速生成透明PNG",
    description: "免费在线图片去背景工具，AI智能抠图5秒完成。支持人物、产品、汽车照片背景移除，一键生成透明PNG。100%免费的AI背景去除器，如何免费去除图片背景的最佳选择。",
    keywords: "移除背景, 透明背景, PNG, AI背景移除, 照片编辑, 图片编辑器, 背景移除器, 抠图, 如何免费去除图片背景, 在线AI抠图工具, 人物图片背景移除, 产品图背景透明化, 汽车照片背景去除, 图片去背景, 智能抠图, 快速生成PNG, 5秒抠图",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  es: {
    title: "Eliminar Fondo de Imagen - 100% Gratis e IA | Remove bg - PNG Transparente Rápido",
    description: "Elimina fondos de imágenes automáticamente con IA en 5 segundos. Sube tu foto y obtén un PNG transparente al instante. 100% gratis para personas, productos, animales, autos y gráficos.",
    keywords: "eliminar fondo, fondo transparente, PNG, eliminación de fondo IA, edición de fotos, editor de imágenes, PNG rápido, 5 segundos, eliminar fondo gratis",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  fr: {
    title: "Supprimer l'Arrière-plan d'Image - 100% Gratuit et IA | Remove bg - PNG Transparent Rapide",
    description: "Supprimez automatiquement les arrière-plans d'images avec l'IA en 5 secondes. Téléchargez votre photo et obtenez un PNG transparent instantanément. 100% gratuit pour personnes, produits, animaux, voitures et graphiques.",
    keywords: "supprimer arrière-plan, arrière-plan transparent, PNG, suppression arrière-plan IA, édition photo, éditeur d'images, PNG rapide, 5 secondes, suppression gratuite",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
  de: {
    title: "Bildhintergrund Entfernen - 100% Kostenlos & KI-Gestützt | Remove bg - Schnelles Transparentes PNG",
    description: "Entfernen Sie Bildhintergründe automatisch mit KI in 5 Sekunden. Laden Sie Ihr Foto hoch und erhalten Sie sofort ein transparentes PNG. 100% kostenlos für Personen, Produkte, Tiere, Autos und Grafiken.",
    keywords: "Hintergrund entfernen, transparenter Hintergrund, PNG, KI Hintergrundentfernung, Fotobearbeitung, Bildbearbeiter, schnelles PNG, 5 Sekunden, kostenlose Entfernung",
    ogType: "website",
    twitterCard: "summary_large_image",
  },
};

export const generateHreflangLinks = (currentPath: string): Array<{ hreflang: string; href: string }> => {
  const baseUrl = "https://www.remove.bg";
  const languages: Language[] = ['en', 'zh', 'es', 'fr', 'de'];
  
  const links: Array<{ hreflang: string; href: string }> = [];
  
  // Remove language prefix from current path
  const pathWithoutLang = currentPath.replace(/^\/(en|zh|es|fr|de)/, '') || '/';
  
  // Add hreflang for each language
  languages.forEach(lang => {
    const href = lang === 'en' 
      ? `${baseUrl}${pathWithoutLang}`
      : `${baseUrl}/${lang}${pathWithoutLang}`;
    
    links.push({
      hreflang: lang,
      href: href
    });
  });
  
  // Add x-default (points to English version)
  links.push({
    hreflang: 'x-default',
    href: `${baseUrl}${pathWithoutLang}`
  });
  
  return links;
};

export const generateCanonicalUrl = (currentPath: string, language: Language): string => {
  const baseUrl = "https://www.remove.bg";
  const pathWithoutLang = currentPath.replace(/^\/(en|zh|es|fr|de)/, '') || '/';
  
  return language === 'en' 
    ? `${baseUrl}${pathWithoutLang}`
    : `${baseUrl}/${language}${pathWithoutLang}`;
};

export const generateJsonLd = (language: Language, currentPath: string) => {
  const baseUrl = "https://www.remove.bg";
  const config = defaultSEOConfig[language];
  
  const baseJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Remove bg",
    "description": config.description,
    "url": generateCanonicalUrl(currentPath, language),
    "applicationCategory": "PhotoEditingApplication",
    "operatingSystem": "Web Browser",
    "inLanguage": language,
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "creator": {
      "@type": "Organization",
      "name": "Remove bg",
      "url": baseUrl
    }
  };

  // Add breadcrumb for non-homepage
  if (currentPath !== '/' && !currentPath.match(/^\/(en|zh|es|fr|de)\/?$/)) {
    return {
      ...baseJsonLd,
      "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": generateCanonicalUrl('/', language)
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": config.title,
            "item": generateCanonicalUrl(currentPath, language)
          }
        ]
      }
    };
  }

  return baseJsonLd;
};

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ImageUploader } from "@/components/ImageUploader";
import { ProcessingModal } from "@/components/ProcessingModal";
import { ImageComparison } from "@/components/ImageComparison";

import { BlogCard } from "@/components/BlogCard";

import { useTranslation, type Language } from "@/lib/i18n";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { type ProcessingStatus, type BlogPost, type UseCase } from "@/lib/types";
import { 
  User, 
  Camera, 
  Megaphone, 
  ShoppingCart, 
  Code, 
  Building,
  ArrowRight,
  Check,
  Star
} from "lucide-react";

interface HomeProps {
  language: Language;
}

export default function Home({ language }: HomeProps) {
  const { t } = useTranslation(language);
  const { toast } = useToast();
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [processId, setProcessId] = useState<number | null>(null);
  const [showProcessing, setShowProcessing] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [activeCategory, setActiveCategory] = useState('products');
  const [activeShowcaseCategory, setActiveShowcaseCategory] = useState('people');

  // Poll for processing status
  const { data: processingStatus } = useQuery({
    queryKey: ['/api/process', processId],
    enabled: !!processId && !showResults,
    refetchInterval: 1000,
    queryFn: async () => {
      const response = await fetch(`/api/process/${processId}`);
      if (!response.ok) throw new Error('Failed to check status');
      return response.json();
    },
  });

  // Handle processing completion
  useEffect(() => {
    if (processingStatus?.status === 'completed') {
      setShowProcessing(false);
      setShowResults(true);

      // Show appropriate success message
      if (processingStatus?.isSimulationMode) {
        toast({
          title: t("demo.mode"),
          description: t("demo.simulation"),
          variant: "default",
        });
      } else {
        toast({
          title: t("processing.success"),
          description: t("processing.apiActive"),
          variant: "default",
        });
      }
    } else if (processingStatus?.status === 'failed') {
      setShowProcessing(false);
      toast({
        title: t("processing.failed"),
        description: t("processing.failedDesc"),
        variant: "destructive",
      });
    }
  }, [processingStatus, toast]);

  const handleUploadStart = (file: File) => {
    setUploadedFile(file);
    setShowResults(false);
  };

  const handleUploadComplete = (id: number) => {
    setProcessId(id);
    setShowProcessing(true);
  };

  const handleProcessingComplete = () => {
    setShowProcessing(false);
    setShowResults(true);
  };

  const handleDownload = async () => {
    if (!processId) return;
    
    try {
      const response = await apiRequest('GET', `/api/download/${processId}`);
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `processed_${uploadedFile?.name || 'image.png'}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const handleProcessAnother = () => {
    setShowResults(false);
    setProcessId(null);
    setUploadedFile(null);
  };



  // Mock data for blog posts
  const blogPosts: BlogPost[] = [
    {
      id: '1',
      title: 'Improve your editing workflow with Retouch4me and Remove bg',
      excerpt: 'Learn how to combine powerful retouching tools for professional results.',
      date: 'May 09, 2025',
      image: 'https://images.unsplash.com/photo-1616469829581-73993eb86b02?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'improve-editing-workflow-retouch4me',
    },
    {
      id: '2',
      title: 'Boost sales with AI-optimized images',
      excerpt: 'Discover how clean product images can increase your conversion rates.',
      date: 'Feb 12, 2025',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'boost-sales-ai-optimized-images',
    },
    {
      id: '3',
      title: 'How to make a background transparent in Paint and Paint 3D',
      excerpt: 'Step-by-step guide for creating transparent backgrounds in Microsoft Paint.',
      date: 'Feb 10, 2025',
      image: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300',
      slug: 'transparent-background-paint-paint3d',
    },
  ];

  // Use cases data
  const useCases: UseCase[] = [
    {
      id: 'individuals',
      title: t("usecases.individuals.title"),
      description: t("usecases.individuals.description"),
      icon: 'User',
      href: '/individuals',
    },
    {
      id: 'photographers',
      title: t("usecases.photographers.title"),
      description: t("usecases.photographers.description"),
      icon: 'Camera',
      href: '/photographers',
    },
    {
      id: 'marketing',
      title: t("usecases.marketing.title"),
      description: t("usecases.marketing.description"),
      icon: 'Megaphone',
      href: '/marketing',
    },
    {
      id: 'ecommerce',
      title: t("usecases.ecommerce.title"),
      description: t("usecases.ecommerce.description"),
      icon: 'ShoppingCart',
      href: '/ecommerce',
    },
    {
      id: 'developers',
      title: t("usecases.developers.title"),
      description: t("usecases.developers.description"),
      icon: 'Code',
      href: '/developers',
    },
    {
      id: 'enterprise',
      title: t("usecases.enterprise.title"),
      description: t("usecases.enterprise.description"),
      icon: 'Building',
      href: '/enterprise',
    },
  ];

  const getIcon = (iconName: string) => {
    const icons = {
      User,
      Camera,
      Megaphone,
      ShoppingCart,
      Code,
      Building,
    };
    return icons[iconName as keyof typeof icons];
  };

  const getCategoryImage = (category: string) => {
    const categoryImages = {
      products: 'https://images.unsplash.com/photo-1484704849700-f032a568e944?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600',
      people: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600',
      animals: 'https://images.unsplash.com/photo-1552053831-71594a27632d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600',
      cars: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600',
      graphics: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=600',
    };
    return categoryImages[category as keyof typeof categoryImages] || categoryImages.products;
  };

  const getCategoryTransparentImage = (category: string) => {
    // 使用本地透明背景PNG图片来展示去除背景的效果
    // 按照顺序分配：People、Animals、Cars、Graphics
    const transparentImages = {
      products: '/images/removebg-preview1.png', // 产品示例
      people: '/images/removebg-preview2.png',   // 人物示例
      animals: '/images/removebg-preview3.png',  // 动物示例
      cars: '/images/removebg-preview4.png',     // 汽车示例
      graphics: '/images/removebg-preview5.png', // 图形示例
    };
    return transparentImages[category as keyof typeof transparentImages] || transparentImages.products;
  };

  return (
    <>
      {/* Hero Section */}
      <section className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-slide-up">
              {t("hero.title.line1")}<br />
              <span className="gradient-text">{t("hero.title.line2")}</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto animate-slide-up" style={{ animationDelay: '0.2s' }}>
              {t("hero.description")}
            </p>
          </div>

          <ImageUploader
            language={language}
            onUploadStart={handleUploadStart}
            onUploadComplete={handleUploadComplete}
          />
        </div>
      </section>

      {/* Processing Modal */}
      <ProcessingModal
        open={showProcessing}
        language={language}
        onComplete={handleProcessingComplete}
      />

      {/* Results Section */}
      {showResults && uploadedFile && processingStatus && (
        <ImageComparison
          language={language}
          originalImage={URL.createObjectURL(uploadedFile)}
          processedImage={`/uploads/${processingStatus.processedPath}`}
          onProcessAnother={handleProcessAnother}
          onDownload={handleDownload}
        />
      )}

      {/* Features Section */}
      <section className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">{t("features.title")}</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t("features.description")}
            </p>
          </div>

          {/* Category Tabs */}
          <div className="flex flex-wrap justify-center mb-12">
            <div className="flex space-x-2 bg-gray-100 rounded-xl p-1">
              {['products', 'people', 'animals', 'cars', 'graphics'].map((category) => (
                <Button
                  key={category}
                  variant="ghost"
                  onClick={() => setActiveCategory(category)}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                    activeCategory === category
                      ? 'bg-white text-purple-600 shadow-sm'
                      : 'text-gray-600 hover:text-purple-600'
                  }`}
                >
                  {t(`categories.${category}` as any)}
                </Button>
              ))}
            </div>
          </div>

          {/* Before/After Examples */}
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card className="shadow-lg">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 text-center">
                  {t("results.original")}
                </h3>
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={getCategoryImage(activeCategory)}
                    alt={`${activeCategory} original image before background removal - AI background remover example`}
                    className="w-full h-full object-cover"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 text-center">
                  {t("results.transparent")}
                </h3>
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative checkerboard p-4">
                  <img
                    src={getCategoryTransparentImage(activeCategory)}
                    alt={`${activeCategory} with transparent background removed - free background remover result`}
                    className="w-full h-full object-contain"
                    style={{
                      filter: 'drop-shadow(0 0 10px rgba(0,0,0,0.3))',
                      background: 'transparent'
                    }}
                  />
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="w-full h-full bg-gradient-to-br from-transparent via-white/20 to-transparent"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>


        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">{t("usecases.title")}</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t("usecases.description")}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {useCases.map((useCase) => {
              const IconComponent = getIcon(useCase.icon);
              return (
                <Card key={useCase.id} className="shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardContent className="p-6">
                    <div className="gradient-bg rounded-lg w-12 h-12 flex items-center justify-center mb-4">
                      {IconComponent && <IconComponent className="h-6 w-6 text-white" />}
                    </div>
                    <h3 className="text-xl font-semibold mb-3">{useCase.title}</h3>
                    <p className="text-gray-600 mb-4">{useCase.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>



      {/* Showcase Section */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Just picture it</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See the magic in action with these before and after transformations
            </p>
          </div>

          {/* Showcase Categories */}
          <div className="flex flex-wrap justify-center mb-12">
            <div className="flex space-x-2 bg-white rounded-xl p-1 shadow-sm">
              {['people', 'products', 'animals', 'cars', 'graphics'].map((category) => (
                <Button
                  key={category}
                  variant="ghost"
                  onClick={() => setActiveShowcaseCategory(category)}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                    activeShowcaseCategory === category
                      ? 'gradient-bg text-white shadow-sm'
                      : 'text-gray-600 hover:text-purple-600'
                  }`}
                >
                  {t(`categories.${category}` as any)}
                </Button>
              ))}
            </div>
          </div>

          {/* Showcase Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
            {/* Step 1: Original */}
            <div className="text-center">
              <Card className="shadow-lg mb-4">
                <CardContent className="p-4">
                  <img
                    src={getCategoryImage(activeShowcaseCategory)}
                    alt="Original image before AI background removal - photo editor background example"
                    className="w-full aspect-square object-cover rounded-lg"
                  />
                </CardContent>
              </Card>
              <h3 className="font-semibold text-lg">Original</h3>
            </div>

            {/* Step 2: Transparent Background */}
            <div className="text-center">
              <Card className="shadow-lg mb-4">
                <CardContent className="p-4">
                  <div className="w-full aspect-square rounded-lg flex items-center justify-center relative checkerboard">
                    <img
                      src={getCategoryTransparentImage(activeShowcaseCategory)}
                      alt="Transparent PNG background removed - cut out image result from AI background remover"
                      className="w-full h-full object-contain p-4"
                    />
                  </div>
                </CardContent>
              </Card>
              <h3 className="font-semibold text-lg">Transparent background</h3>
            </div>
          </div>
        </div>
      </section>

      {/* SEO Optimized Sections */}
      <section className="py-16 lg:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">{t("seo.product_photos.title")}</h2>
              <p className="text-gray-600">{t("seo.product_photos.description")}</p>
            </div>
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">{t("seo.car_images.title")}</h2>
              <p className="text-gray-600">{t("seo.car_images.description")}</p>
            </div>
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">{t("seo.transparent_png.title")}</h2>
              <p className="text-gray-600">{t("seo.transparent_png.description")}</p>
            </div>
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section className="py-16 lg:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">{t("blog.title")}</h2>
              <p className="text-xl text-gray-600">{t("blog.description")}</p>
            </div>
            <a href="/blog" className="text-purple-600 font-medium hover:underline">
              {t("blog.see_all")}
            </a>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <BlogCard key={post.id} post={post} />
            ))}
          </div>
        </div>
      </section>


    </>
  );
}

import { useTranslation, type Language } from "@/lib/i18n";
import { type SampleImage } from "@/lib/types";

interface SampleGalleryProps {
  language: Language;
  onSampleSelect: (file: File) => void;
}

export function SampleGallery({ language, onSampleSelect }: SampleGalleryProps) {
  const { t } = useTranslation(language);

  const sampleImages: SampleImage[] = [
    {
      id: '1',
      url: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=300',
      category: 'people',
      alt: 'Professional woman portrait for people background removal - AI background remover sample',
    },
    {
      id: '2',
      url: 'https://images.unsplash.com/photo-1552053831-71594a27632d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&h=300',
      category: 'animals',
      alt: 'Golden retriever dog for animal background removal - free background remover tool',
    },
    {
      id: '3',
      url: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&h=300',
      category: 'cars',
      alt: 'Red sports car for car image background removal - background remover for car images',
    },
    {
      id: '4',
      url: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&h=300',
      category: 'products',
      alt: 'Modern smartphone for product photo background removal - remove background from product photos',
    },
  ];

  const handleSampleClick = async (image: SampleImage) => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const file = new File([blob], `sample-${image.id}.jpg`, { type: blob.type });
      onSampleSelect(file);
    } catch (error) {
      console.error('Failed to fetch sample image:', error);
    }
  };

  return (
    <div className="mt-8">
      <p className="text-center text-gray-600 mb-4">{t("samples.title")}</p>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {sampleImages.map((image) => (
          <button
            key={image.id}
            onClick={() => handleSampleClick(image)}
            className="w-full h-24 rounded-lg overflow-hidden hover:scale-105 transition-transform duration-200"
          >
            <img
              src={image.url}
              alt={image.alt}
              className="w-full h-full object-cover"
            />
          </button>
        ))}
      </div>
    </div>
  );
}

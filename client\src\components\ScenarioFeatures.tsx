import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useTranslation, type Language } from "@/lib/i18n";
import { 
  Zap, 
  Shield, 
  Download, 
  Clock, 
  Star, 
  Palette,
  Target,
  Sparkles,
  Award,
  Users
} from "lucide-react";

interface ScenarioFeaturesProps {
  language: Language;
  scenario: 'products' | 'animals' | 'cars';
}

export function ScenarioFeatures({ language, scenario }: ScenarioFeaturesProps) {
  const { t } = useTranslation(language);

  // 图标映射
  const iconMap = {
    zap: Zap,
    shield: Shield,
    download: Download,
    clock: Clock,
    star: Star,
    palette: Palette,
    target: Target,
    sparkles: Sparkles,
    award: Award,
    users: Users
  };

  // 场景特定功能配置
  const scenarioFeatures = {
    products: [
      {
        icon: 'target',
        title: t('products.features.precision.title'),
        description: t('products.features.precision.description'),
        highlight: t('products.features.precision.highlight')
      },
      {
        icon: 'palette',
        title: t('products.features.quality.title'),
        description: t('products.features.quality.description'),
        highlight: t('products.features.quality.highlight')
      },
      {
        icon: 'zap',
        title: t('products.features.speed.title'),
        description: t('products.features.speed.description'),
        highlight: t('products.features.speed.highlight')
      },
      {
        icon: 'download',
        title: t('products.features.formats.title'),
        description: t('products.features.formats.description'),
        highlight: t('products.features.formats.highlight')
      }
    ],
    animals: [
      {
        icon: 'sparkles',
        title: t('animals.features.fur.title'),
        description: t('animals.features.fur.description'),
        highlight: t('animals.features.fur.highlight')
      },
      {
        icon: 'target',
        title: t('animals.features.detection.title'),
        description: t('animals.features.detection.description'),
        highlight: t('animals.features.detection.highlight')
      },
      {
        icon: 'palette',
        title: t('animals.features.natural.title'),
        description: t('animals.features.natural.description'),
        highlight: t('animals.features.natural.highlight')
      },
      {
        icon: 'clock',
        title: t('animals.features.instant.title'),
        description: t('animals.features.instant.description'),
        highlight: t('animals.features.instant.highlight')
      }
    ],
    cars: [
      {
        icon: 'award',
        title: t('cars.features.precision.title'),
        description: t('cars.features.precision.description'),
        highlight: t('cars.features.precision.highlight')
      },
      {
        icon: 'sparkles',
        title: t('cars.features.reflection.title'),
        description: t('cars.features.reflection.description'),
        highlight: t('cars.features.reflection.highlight')
      },
      {
        icon: 'star',
        title: t('cars.features.professional.title'),
        description: t('cars.features.professional.description'),
        highlight: t('cars.features.professional.highlight')
      },
      {
        icon: 'users',
        title: t('cars.features.dealership.title'),
        description: t('cars.features.dealership.description'),
        highlight: t('cars.features.dealership.highlight')
      }
    ]
  };

  const features = scenarioFeatures[scenario];

  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <Badge variant="secondary" className="gradient-bg text-white px-4 py-2 mb-4">
            {t(`${scenario}.features.badge`)}
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {t(`${scenario}.features.title`)}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t(`${scenario}.features.description`)}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {features.map((feature, index) => {
            const IconComponent = iconMap[feature.icon as keyof typeof iconMap];
            return (
              <Card key={index} className="relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group border-0 bg-gradient-to-br from-white to-gray-50">
                <CardContent className="p-6 text-center">
                  {/* Icon */}
                  <div className="gradient-bg rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                    {IconComponent && <IconComponent className="w-8 h-8 text-white" />}
                  </div>

                  {/* Title */}
                  <h3 className="text-lg font-semibold mb-3">{feature.title}</h3>

                  {/* Description */}
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {feature.description}
                  </p>

                  {/* Highlight */}
                  <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg p-3 border-l-4 border-purple-500">
                    <p className="text-sm font-medium text-purple-800">
                      {feature.highlight}
                    </p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Stats Section */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-8 text-white">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">{t(`${scenario}.stats.processed`)}</div>
              <div className="text-purple-100">{t(`${scenario}.stats.processed_label`)}</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">{t(`${scenario}.stats.accuracy`)}</div>
              <div className="text-purple-100">{t(`${scenario}.stats.accuracy_label`)}</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">{t(`${scenario}.stats.time`)}</div>
              <div className="text-purple-100">{t(`${scenario}.stats.time_label`)}</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">{t(`${scenario}.stats.satisfaction`)}</div>
              <div className="text-purple-100">{t(`${scenario}.stats.satisfaction_label`)}</div>
            </div>
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-center mb-8">
            {t(`${scenario}.why.title`)}
          </h3>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <Shield className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h4 className="font-semibold mb-2">{t(`${scenario}.why.secure.title`)}</h4>
              <p className="text-gray-600 text-sm">{t(`${scenario}.why.secure.description`)}</p>
            </div>
            <div className="text-center">
              <Zap className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
              <h4 className="font-semibold mb-2">{t(`${scenario}.why.fast.title`)}</h4>
              <p className="text-gray-600 text-sm">{t(`${scenario}.why.fast.description`)}</p>
            </div>
            <div className="text-center">
              <Star className="w-12 h-12 text-purple-500 mx-auto mb-4" />
              <h4 className="font-semibold mb-2">{t(`${scenario}.why.quality.title`)}</h4>
              <p className="text-gray-600 text-sm">{t(`${scenario}.why.quality.description`)}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

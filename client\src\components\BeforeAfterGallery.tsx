import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useTranslation, type Language } from "@/lib/i18n";
import { Eye, Download, ArrowRight } from "lucide-react";

interface BeforeAfterItem {
  id: string;
  title: string;
  beforeImage: string;
  afterImage: string;
  category: string;
}

interface BeforeAfterGalleryProps {
  language: Language;
  scenario: 'products' | 'animals' | 'cars';
  onTryNow: () => void;
}

export function BeforeAfterGallery({ language, scenario, onTryNow }: BeforeAfterGalleryProps) {
  const { t } = useTranslation(language);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);

  // 示例数据 - 在实际应用中这些应该来自 API 或配置文件
  const galleryData = {
    products: [
      {
        id: 'product-1',
        title: t('products.gallery.item1.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('products.gallery.item1.category')
      },
      {
        id: 'product-2', 
        title: t('products.gallery.item2.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('products.gallery.item2.category')
      },
      {
        id: 'product-3',
        title: t('products.gallery.item3.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('products.gallery.item3.category')
      }
    ],
    animals: [
      {
        id: 'animal-1',
        title: t('animals.gallery.item1.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('animals.gallery.item1.category')
      },
      {
        id: 'animal-2',
        title: t('animals.gallery.item2.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('animals.gallery.item2.category')
      },
      {
        id: 'animal-3',
        title: t('animals.gallery.item3.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('animals.gallery.item3.category')
      }
    ],
    cars: [
      {
        id: 'car-1',
        title: t('cars.gallery.item1.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('cars.gallery.item1.category')
      },
      {
        id: 'car-2',
        title: t('cars.gallery.item2.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('cars.gallery.item2.category')
      },
      {
        id: 'car-3',
        title: t('cars.gallery.item3.title'),
        beforeImage: '/api/placeholder/300/300',
        afterImage: '/api/placeholder/300/300',
        category: t('cars.gallery.item3.category')
      }
    ]
  };

  const items = galleryData[scenario];

  return (
    <section className="py-16 lg:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {t(`${scenario}.gallery.title`)}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t(`${scenario}.gallery.description`)}
          </p>
        </div>

        {/* Gallery Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {items.map((item) => (
            <Card key={item.id} className="overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-0">
                {/* Before/After Images */}
                <div className="relative h-64 overflow-hidden">
                  <div className="absolute inset-0 grid grid-cols-2">
                    {/* Before Image */}
                    <div className="relative overflow-hidden">
                      <img 
                        src={item.beforeImage} 
                        alt={`${item.title} - Before`}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-2 left-2">
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                          {t('common.before')}
                        </Badge>
                      </div>
                    </div>
                    
                    {/* After Image */}
                    <div className="relative overflow-hidden">
                      <img 
                        src={item.afterImage} 
                        alt={`${item.title} - After`}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-2 right-2">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {t('common.after')}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  {/* Divider Line */}
                  <div className="absolute top-0 left-1/2 w-0.5 h-full bg-white shadow-lg transform -translate-x-0.5"></div>
                </div>

                {/* Card Content */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <Badge variant="outline" className="text-xs">
                      {item.category}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedItem(selectedItem === item.id ? null : item.id)}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <p className="text-lg text-gray-600 mb-6">
            {t(`${scenario}.gallery.cta_text`)}
          </p>
          <Button 
            onClick={onTryNow}
            size="lg" 
            className="gradient-bg hover:opacity-90 text-white px-8 py-3"
          >
            {t(`${scenario}.gallery.cta_button`)}
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}

import React from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useTranslation, type Language } from "@/lib/i18n";
import { ArrowRight, Star, Check } from "lucide-react";

interface ScenarioHeroProps {
  language: Language;
  scenario: 'products' | 'animals' | 'cars';
  onScrollToUpload: () => void;
}

export function ScenarioHero({ language, scenario, onScrollToUpload }: ScenarioHeroProps) {
  const { t } = useTranslation(language);

  const scenarioConfig = {
    products: {
      badge: t(`${scenario}.badge`),
      title: t(`${scenario}.hero.title`),
      subtitle: t(`${scenario}.hero.subtitle`),
      description: t(`${scenario}.hero.description`),
      features: [
        t(`${scenario}.hero.feature1`),
        t(`${scenario}.hero.feature2`),
        t(`${scenario}.hero.feature3`)
      ],
      cta: t(`${scenario}.hero.cta`)
    },
    animals: {
      badge: t(`${scenario}.badge`),
      title: t(`${scenario}.hero.title`),
      subtitle: t(`${scenario}.hero.subtitle`),
      description: t(`${scenario}.hero.description`),
      features: [
        t(`${scenario}.hero.feature1`),
        t(`${scenario}.hero.feature2`),
        t(`${scenario}.hero.feature3`)
      ],
      cta: t(`${scenario}.hero.cta`)
    },
    cars: {
      badge: t(`${scenario}.badge`),
      title: t(`${scenario}.hero.title`),
      subtitle: t(`${scenario}.hero.subtitle`),
      description: t(`${scenario}.hero.description`),
      features: [
        t(`${scenario}.hero.feature1`),
        t(`${scenario}.hero.feature2`),
        t(`${scenario}.hero.feature3`)
      ],
      cta: t(`${scenario}.hero.cta`)
    }
  };

  const config = scenarioConfig[scenario];

  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          {/* Badge */}
          <div className="flex justify-center mb-6">
            <Badge variant="secondary" className="gradient-bg text-white px-4 py-2 text-sm font-medium">
              <Star className="w-4 h-4 mr-2" />
              {config.badge}
            </Badge>
          </div>

          {/* Title */}
          <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-slide-up">
            {config.title}
            <br />
            <span className="gradient-text">{config.subtitle}</span>
          </h1>

          {/* Description */}
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8 animate-slide-up" style={{ animationDelay: '0.2s' }}>
            {config.description}
          </p>

          {/* Features */}
          <div className="flex flex-wrap justify-center gap-4 mb-8 animate-slide-up" style={{ animationDelay: '0.4s' }}>
            {config.features.map((feature, index) => (
              <div key={index} className="flex items-center bg-white rounded-full px-4 py-2 shadow-sm border">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                <span className="text-sm font-medium text-gray-700">{feature}</span>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <Button 
            onClick={onScrollToUpload}
            size="lg" 
            className="gradient-bg hover:opacity-90 text-white px-8 py-3 text-lg font-semibold animate-slide-up"
            style={{ animationDelay: '0.6s' }}
          >
            {config.cta}
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}
